import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  FlatList,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';

import { getFeaturedTracks, getCategories, getMoods } from '../data/musicData';
import { useMusicPlayer } from '../services/MusicPlayerContext';

const { width } = Dimensions.get('window');

const HomeScreen = ({ navigation }) => {
  const { playTrack } = useMusicPlayer();
  const featuredTracks = getFeaturedTracks();
  const categories = getCategories();
  const moods = getMoods();

  const handleTrackPress = (track) => {
    playTrack(track, featuredTracks, featuredTracks.findIndex(t => t.id === track.id));
    navigation.navigate('Player');
  };

  const handleCategoryPress = (category) => {
    navigation.navigate('Category', { 
      categoryName: category.name,
      categoryType: 'category',
      categoryId: category.id 
    });
  };

  const handleMoodPress = (mood) => {
    navigation.navigate('Category', { 
      categoryName: mood.name,
      categoryType: 'mood',
      categoryId: mood.id 
    });
  };

  const renderFeaturedTrack = ({ item }) => (
    <TouchableOpacity
      style={styles.featuredTrack}
      onPress={() => handleTrackPress(item)}
    >
      <Image source={{ uri: item.coverImage }} style={styles.featuredCover} />
      <LinearGradient
        colors={['transparent', 'rgba(0,0,0,0.8)']}
        style={styles.featuredGradient}
      >
        <View style={styles.featuredInfo}>
          <Text style={styles.featuredTitle} numberOfLines={1}>
            {item.title}
          </Text>
          <Text style={styles.featuredArtist} numberOfLines={1}>
            {item.artist}
          </Text>
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );

  const renderCategory = ({ item }) => (
    <TouchableOpacity
      style={[styles.categoryCard, { backgroundColor: item.color }]}
      onPress={() => handleCategoryPress(item)}
    >
      <Ionicons name={item.icon} size={30} color="white" />
      <Text style={styles.categoryName}>{item.name}</Text>
      <Text style={styles.categoryDescription}>{item.description}</Text>
    </TouchableOpacity>
  );

  const renderMood = ({ item }) => (
    <TouchableOpacity
      style={[styles.moodCard, { backgroundColor: item.color }]}
      onPress={() => handleMoodPress(item)}
    >
      <Ionicons name={item.icon} size={24} color="white" />
      <Text style={styles.moodName}>{item.name}</Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.greeting}>Good evening</Text>
          <Text style={styles.title}>What do you want to hear?</Text>
        </View>

        {/* Featured Tracks */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Featured Tracks</Text>
          <FlatList
            data={featuredTracks}
            renderItem={renderFeaturedTrack}
            keyExtractor={(item) => item.id}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.featuredList}
          />
        </View>

        {/* Categories */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Browse by Genre</Text>
          <FlatList
            data={categories}
            renderItem={renderCategory}
            keyExtractor={(item) => item.id}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoryList}
          />
        </View>

        {/* Moods */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Browse by Mood</Text>
          <FlatList
            data={moods}
            renderItem={renderMood}
            keyExtractor={(item) => item.id}
            numColumns={2}
            scrollEnabled={false}
            contentContainerStyle={styles.moodGrid}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0f0f23',
  },
  header: {
    padding: 20,
    paddingTop: 10,
  },
  greeting: {
    fontSize: 16,
    color: '#8e8e93',
    marginBottom: 5,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#ffffff',
  },
  section: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 15,
    paddingHorizontal: 20,
  },
  featuredList: {
    paddingHorizontal: 15,
  },
  featuredTrack: {
    width: width * 0.7,
    height: 200,
    marginHorizontal: 5,
    borderRadius: 15,
    overflow: 'hidden',
  },
  featuredCover: {
    width: '100%',
    height: '100%',
  },
  featuredGradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: '50%',
    justifyContent: 'flex-end',
  },
  featuredInfo: {
    padding: 15,
  },
  featuredTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 5,
  },
  featuredArtist: {
    fontSize: 14,
    color: '#cccccc',
  },
  categoryList: {
    paddingHorizontal: 15,
  },
  categoryCard: {
    width: 150,
    height: 120,
    marginHorizontal: 5,
    borderRadius: 15,
    padding: 15,
    justifyContent: 'center',
    alignItems: 'center',
  },
  categoryName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#ffffff',
    marginTop: 8,
    marginBottom: 4,
  },
  categoryDescription: {
    fontSize: 12,
    color: '#ffffff',
    opacity: 0.8,
    textAlign: 'center',
  },
  moodGrid: {
    paddingHorizontal: 20,
  },
  moodCard: {
    flex: 1,
    height: 80,
    margin: 5,
    borderRadius: 12,
    padding: 15,
    flexDirection: 'row',
    alignItems: 'center',
  },
  moodName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
    marginLeft: 12,
  },
});

export default HomeScreen;
