import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';

import { getMusicByCategory, getMusicByMood, getCategories, getMoods } from '../data/musicData';
import { useMusicPlayer } from '../services/MusicPlayerContext';

const CategoryScreen = ({ route, navigation }) => {
  const { categoryName, categoryType, categoryId } = route.params;
  const { playTrack, isFavorite, addToFavorites, removeFromFavorites } = useMusicPlayer();

  // Get tracks based on category type
  const getTracks = () => {
    if (categoryType === 'category') {
      return getMusicByCategory(categoryName);
    } else if (categoryType === 'mood') {
      return getMusicByMood(categoryName);
    }
    return [];
  };

  // Get category/mood info for styling
  const getCategoryInfo = () => {
    if (categoryType === 'category') {
      return getCategories().find(cat => cat.id === categoryId);
    } else if (categoryType === 'mood') {
      return getMoods().find(mood => mood.id === categoryId);
    }
    return null;
  };

  const tracks = getTracks();
  const categoryInfo = getCategoryInfo();

  const handleTrackPress = (track) => {
    const trackIndex = tracks.findIndex(t => t.id === track.id);
    playTrack(track, tracks, trackIndex);
    navigation.navigate('Player');
  };

  const handleFavoritePress = (track) => {
    if (isFavorite(track.id)) {
      removeFromFavorites(track.id);
    } else {
      addToFavorites(track.id);
    }
  };

  const formatDuration = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handlePlayAll = () => {
    if (tracks.length > 0) {
      playTrack(tracks[0], tracks, 0);
      navigation.navigate('Player');
    }
  };

  const renderTrack = ({ item, index }) => (
    <TouchableOpacity
      style={styles.trackItem}
      onPress={() => handleTrackPress(item)}
    >
      <View style={styles.trackNumber}>
        <Text style={styles.trackNumberText}>{index + 1}</Text>
      </View>
      <Image source={{ uri: item.coverImage }} style={styles.trackCover} />
      <View style={styles.trackInfo}>
        <Text style={styles.trackTitle} numberOfLines={1}>
          {item.title}
        </Text>
        <Text style={styles.trackArtist} numberOfLines={1}>
          {item.artist}
        </Text>
        <Text style={styles.trackDuration}>{formatDuration(item.duration)}</Text>
      </View>
      <TouchableOpacity
        style={styles.favoriteButton}
        onPress={() => handleFavoritePress(item)}
      >
        <Ionicons
          name={isFavorite(item.id) ? 'heart' : 'heart-outline'}
          size={20}
          color={isFavorite(item.id) ? '#FF6B9D' : '#8e8e93'}
        />
      </TouchableOpacity>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header with gradient */}
      <LinearGradient
        colors={[categoryInfo?.color || '#6B73FF', 'transparent']}
        style={styles.headerGradient}
      >
        <View style={styles.headerContent}>
          <View style={styles.categoryIcon}>
            <Ionicons
              name={categoryInfo?.icon || 'musical-notes'}
              size={40}
              color="#ffffff"
            />
          </View>
          <Text style={styles.categoryTitle}>{categoryName}</Text>
          <Text style={styles.categoryDescription}>
            {categoryInfo?.description || `${categoryType} collection`}
          </Text>
          <Text style={styles.trackCount}>
            {tracks.length} song{tracks.length !== 1 ? 's' : ''}
          </Text>
        </View>
      </LinearGradient>

      {/* Play All Button */}
      {tracks.length > 0 && (
        <View style={styles.playAllContainer}>
          <TouchableOpacity
            style={[
              styles.playAllButton,
              { backgroundColor: categoryInfo?.color || '#6B73FF' }
            ]}
            onPress={handlePlayAll}
          >
            <Ionicons name="play" size={20} color="#ffffff" />
            <Text style={styles.playAllText}>Play All</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Tracks List */}
      <FlatList
        data={tracks}
        renderItem={renderTrack}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.tracksList}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="musical-notes-outline" size={64} color="#8e8e93" />
            <Text style={styles.emptyText}>No songs in this {categoryType}</Text>
            <Text style={styles.emptySubtext}>
              Check back later for new additions
            </Text>
          </View>
        }
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0f0f23',
  },
  headerGradient: {
    paddingTop: 20,
    paddingBottom: 40,
  },
  headerContent: {
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  categoryIcon: {
    width: 80,
    height: 80,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  categoryTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 8,
    textAlign: 'center',
  },
  categoryDescription: {
    fontSize: 16,
    color: '#ffffff',
    opacity: 0.8,
    marginBottom: 8,
    textAlign: 'center',
  },
  trackCount: {
    fontSize: 14,
    color: '#ffffff',
    opacity: 0.7,
  },
  playAllContainer: {
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  playAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 15,
    borderRadius: 25,
  },
  playAllText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  tracksList: {
    paddingHorizontal: 20,
  },
  trackItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#1a1a2e',
  },
  trackNumber: {
    width: 30,
    alignItems: 'center',
    marginRight: 15,
  },
  trackNumberText: {
    fontSize: 14,
    color: '#8e8e93',
    fontWeight: '500',
  },
  trackCover: {
    width: 50,
    height: 50,
    borderRadius: 6,
    marginRight: 15,
  },
  trackInfo: {
    flex: 1,
  },
  trackTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
    marginBottom: 4,
  },
  trackArtist: {
    fontSize: 14,
    color: '#8e8e93',
    marginBottom: 2,
  },
  trackDuration: {
    fontSize: 12,
    color: '#666666',
  },
  favoriteButton: {
    padding: 10,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#ffffff',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#8e8e93',
    textAlign: 'center',
  },
});

export default CategoryScreen;
