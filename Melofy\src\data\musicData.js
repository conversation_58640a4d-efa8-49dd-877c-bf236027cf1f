// Music data structure for Melofy
// This file contains all the music metadata and will be manually managed

export const musicData = {
  // Featured tracks that will be displayed on the home screen
  featured: [
    {
      id: 'f1',
      title: 'Midnight Dreams',
      artist: 'Luna Waves',
      album: 'Nocturnal Vibes',
      duration: 245, // in seconds
      filePath: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav', // Placeholder
      coverImage: 'https://picsum.photos/300/300?random=1',
      category: 'LoFi',
      mood: 'Relax',
      isFeatured: true
    },
    {
      id: 'f2',
      title: 'Focus Flow',
      artist: 'Study Beats',
      album: 'Concentration',
      duration: 180,
      filePath: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav', // Placeholder
      coverImage: 'https://picsum.photos/300/300?random=2',
      category: 'LoFi',
      mood: 'Focus',
      isFeatured: true
    },
    {
      id: 'f3',
      title: 'Happy Vibes',
      artist: 'Sunny Day',
      album: 'Good Mood',
      duration: 210,
      filePath: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav', // Placeholder
      coverImage: 'https://picsum.photos/300/300?random=3',
      category: 'Pop',
      mood: 'Happy',
      isFeatured: true
    }
  ],

  // All tracks organized by categories
  tracks: [
    // LoFi Category
    {
      id: '1',
      title: 'Midnight Dreams',
      artist: 'Luna Waves',
      album: 'Nocturnal Vibes',
      duration: 245,
      filePath: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav', // Placeholder
      coverImage: 'https://picsum.photos/300/300?random=4',
      category: 'LoFi',
      mood: 'Relax'
    },
    {
      id: '2',
      title: 'Focus Flow',
      artist: 'Study Beats',
      album: 'Concentration',
      duration: 180,
      filePath: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav', // Placeholder
      coverImage: 'https://picsum.photos/300/300?random=5',
      category: 'LoFi',
      mood: 'Focus'
    },
    {
      id: '3',
      title: 'Coffee Shop',
      artist: 'Chill Vibes',
      album: 'Urban Sounds',
      duration: 195,
      filePath: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav', // Placeholder
      coverImage: 'https://picsum.photos/300/300?random=6',
      category: 'LoFi',
      mood: 'Relax'
    },

    // Pop Category
    {
      id: '4',
      title: 'Happy Vibes',
      artist: 'Sunny Day',
      album: 'Good Mood',
      duration: 210,
      filePath: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav', // Placeholder
      coverImage: 'https://picsum.photos/300/300?random=7',
      category: 'Pop',
      mood: 'Happy'
    },
    {
      id: '5',
      title: 'Summer Nights',
      artist: 'Beach Party',
      album: 'Endless Summer',
      duration: 225,
      filePath: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav', // Placeholder
      coverImage: 'https://picsum.photos/300/300?random=8',
      category: 'Pop',
      mood: 'Happy'
    },

    // Classical Category
    {
      id: '6',
      title: 'Peaceful Piano',
      artist: 'Classical Ensemble',
      album: 'Serenity',
      duration: 280,
      filePath: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav', // Placeholder
      coverImage: 'https://picsum.photos/300/300?random=9',
      category: 'Classical',
      mood: 'Relax'
    },
    {
      id: '7',
      title: 'Morning Meditation',
      artist: 'Zen Masters',
      album: 'Inner Peace',
      duration: 300,
      filePath: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav', // Placeholder
      coverImage: 'https://picsum.photos/300/300?random=10',
      category: 'Classical',
      mood: 'Focus'
    }
  ],

  // Categories configuration
  categories: [
    {
      id: 'lofi',
      name: 'LoFi',
      description: 'Chill and relaxing beats',
      color: '#6B73FF',
      icon: 'headphones'
    },
    {
      id: 'pop',
      name: 'Pop',
      description: 'Popular and upbeat tracks',
      color: '#FF6B9D',
      icon: 'music'
    },
    {
      id: 'classical',
      name: 'Classical',
      description: 'Timeless classical pieces',
      color: '#4ECDC4',
      icon: 'piano'
    }
  ],

  // Moods configuration
  moods: [
    {
      id: 'relax',
      name: 'Relax',
      description: 'Perfect for unwinding',
      color: '#95E1D3',
      icon: 'leaf'
    },
    {
      id: 'focus',
      name: 'Focus',
      description: 'Enhance your concentration',
      color: '#F38BA8',
      icon: 'target'
    },
    {
      id: 'happy',
      name: 'Happy',
      description: 'Boost your mood',
      color: '#FFD93D',
      icon: 'sun'
    }
  ]
};

// Helper functions to work with music data
export const getMusicByCategory = (category) => {
  return musicData.tracks.filter(track => 
    track.category.toLowerCase() === category.toLowerCase()
  );
};

export const getMusicByMood = (mood) => {
  return musicData.tracks.filter(track => 
    track.mood.toLowerCase() === mood.toLowerCase()
  );
};

export const getFeaturedTracks = () => {
  return musicData.featured;
};

export const getTrackById = (id) => {
  return musicData.tracks.find(track => track.id === id) || 
         musicData.featured.find(track => track.id === id);
};

export const getAllTracks = () => {
  return musicData.tracks;
};

export const getCategories = () => {
  return musicData.categories;
};

export const getMoods = () => {
  return musicData.moods;
};
