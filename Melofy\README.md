# 🎵 Melofy - Music Streaming App

A beautiful, fast, and accessible music streaming application built with React Native (Expo) for Android. Melofy allows users to listen to categorized music instantly without the need for user login or authentication.

## ✨ Features

- **🚀 Instant Access**: No login required - open and start listening immediately
- **🎨 Beautiful UI**: Modern dark theme with smooth animations and gradients
- **📱 Android Optimized**: Built specifically for Android with native performance
- **🎵 Music Categories**: Organized by genres (LoFi, Pop, Classical) and moods (Relax, Focus, Happy)
- **❤️ Local Favorites**: Save favorite tracks locally on device
- **🎛️ Full Player Controls**: Play, pause, skip, seek, repeat, and shuffle
- **🔄 Background Playback**: Continue listening while using other apps
- **🔍 Search & Filter**: Find music by title, artist, album, category, or mood
- **📊 Featured Tracks**: Curated selection on the home screen

## 🏗️ Architecture

### Tech Stack
- **React Native** with **Expo SDK 53**
- **React Navigation** for seamless navigation
- **Expo AV** for audio playback
- **AsyncStorage** for local data persistence
- **Expo Linear Gradient** for beautiful UI effects
- **React Native Vector Icons** for consistent iconography

### Project Structure
```
Melofy/
├── src/
│   ├── components/          # Reusable UI components
│   ├── screens/            # Main app screens
│   │   ├── HomeScreen.js   # Featured tracks and categories
│   │   ├── LibraryScreen.js # Music library with search/filter
│   │   ├── PlayerScreen.js  # Full-screen music player
│   │   └── CategoryScreen.js # Category/mood-specific tracks
│   ├── services/           # Business logic and state management
│   │   └── MusicPlayerContext.js # Global music player state
│   ├── data/              # Data management
│   │   └── musicData.js   # Music metadata and configuration
│   ├── utils/             # Utility functions
│   │   └── assetManager.js # Asset loading and caching
│   └── styles/            # Shared styles and themes
├── assets/                # Static assets
│   ├── music/            # Audio files
│   ├── covers/           # Album cover images
│   └── icons/            # App icons
├── ASSETS_GUIDE.md       # Guide for managing music assets
├── ANDROID_BUILD_GUIDE.md # Android build and deployment guide
└── README.md            # This file
```

## 🚀 Getting Started

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn
- Expo CLI
- Android Studio (for Android development)
- Android device or emulator

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd Melofy
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm start
   # or
   npx expo start
   ```

4. **Run on Android**
   ```bash
   # Using Expo Go app (scan QR code)
   npx expo start
   
   # Or directly open on Android
   npx expo start --android
   ```

5. **Run on Web (for testing)**
   ```bash
   npx expo start --web
   ```

## 📱 Usage

### For Users
1. **Home Screen**: Browse featured tracks and categories
2. **Library**: Search and filter your music collection
3. **Player**: Full-screen player with all controls
4. **Categories**: Browse music by genre or mood
5. **Favorites**: Heart tracks to save them locally

### For Developers
1. **Adding Music**: See `ASSETS_GUIDE.md` for detailed instructions
2. **Customizing Categories**: Edit `src/data/musicData.js`
3. **Styling**: Modify theme colors and styles in component files
4. **Building**: See `ANDROID_BUILD_GUIDE.md` for deployment

## 🎵 Music Management

### Current Setup
The app currently uses placeholder data for demonstration. To add your own music:

1. **Add audio files** to `assets/music/`
2. **Add cover images** to `assets/covers/`
3. **Update** `src/data/musicData.js` with your track information
4. **Test** the app to ensure everything loads correctly

### Supported Formats
- **Audio**: MP3, WAV, M4A, AAC
- **Images**: JPG, PNG (recommended: 300x300px)

## 🔧 Configuration

### App Configuration (app.json)
- **Package Name**: `com.melofy.app`
- **Theme**: Dark theme with purple accent colors
- **Permissions**: Wake lock and foreground service for background playback
- **Icons**: Adaptive icon support for Android

### Audio Configuration
- **Background Playback**: Enabled
- **Audio Focus**: Properly managed for interruptions
- **Formats**: Optimized for mobile playback

## 🎨 Design System

### Colors
- **Primary**: `#6B73FF` (Purple)
- **Secondary**: `#FF6B9D` (Pink)
- **Background**: `#0f0f23` (Dark Blue)
- **Surface**: `#1a1a2e` (Lighter Dark)
- **Text**: `#ffffff` (White)
- **Text Secondary**: `#8e8e93` (Gray)

### Typography
- **Headers**: Bold, white text
- **Body**: Regular weight, appropriate contrast
- **Captions**: Smaller, muted text for metadata

## 🚀 Performance

### Optimizations
- **Asset Caching**: Intelligent loading and caching of images and audio
- **Memory Management**: Proper cleanup of audio resources
- **UI Performance**: Smooth 60fps animations and transitions
- **Bundle Size**: Optimized for mobile download and storage

### Monitoring
- Use React Native Performance Monitor
- Monitor memory usage during playback
- Test on various Android devices and versions

## 🔒 Privacy & Security

### Data Handling
- **No User Accounts**: No personal data collection
- **Local Storage**: Favorites stored locally on device
- **No Analytics**: Privacy-focused approach
- **Offline Capable**: Works without internet (for local assets)

## 🛠️ Development

### Available Scripts
```bash
npm start          # Start Expo development server
npm run android    # Run on Android
npm run web        # Run on web
```

### Development Workflow
1. **Start development server**: `npm start`
2. **Make changes** to source code
3. **Test on device/emulator**
4. **Hot reload** automatically updates the app

### Debugging
- Use Expo DevTools for debugging
- React Native Debugger for advanced debugging
- Android Studio for native debugging

## 📦 Building & Deployment

### Development Build
```bash
npx expo start --android
```

### Production Build
```bash
# Using EAS Build (recommended)
npx eas build --platform android

# Or local build
npx expo prebuild --platform android
cd android && ./gradlew assembleRelease
```

See `ANDROID_BUILD_GUIDE.md` for detailed build instructions.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- **Expo Team** for the excellent development platform
- **React Native Community** for the robust ecosystem
- **Music Artists** (placeholder content for demonstration)

## 📞 Support

For questions, issues, or contributions:
- Create an issue on GitHub
- Check the documentation files in the repository
- Review the troubleshooting sections in the guides

---

**Melofy** - Simple, fast, beautiful music streaming for Android 🎵
