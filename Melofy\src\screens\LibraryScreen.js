import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  TextInput,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';

import { getAllTracks, getCategories, getMoods } from '../data/musicData';
import { useMusicPlayer } from '../services/MusicPlayerContext';

const LibraryScreen = ({ navigation }) => {
  const { playTrack, isFavorite, addToFavorites, removeFromFavorites } = useMusicPlayer();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all'); // 'all', 'favorites', category, mood

  const allTracks = getAllTracks();
  const categories = getCategories();
  const moods = getMoods();

  // Filter tracks based on search and selected filter
  const getFilteredTracks = () => {
    let tracks = allTracks;

    // Apply filter
    if (selectedFilter === 'favorites') {
      tracks = tracks.filter(track => isFavorite(track.id));
    } else if (selectedFilter !== 'all') {
      // Check if it's a category or mood filter
      const category = categories.find(cat => cat.id === selectedFilter);
      const mood = moods.find(m => m.id === selectedFilter);
      
      if (category) {
        tracks = tracks.filter(track => track.category.toLowerCase() === category.name.toLowerCase());
      } else if (mood) {
        tracks = tracks.filter(track => track.mood.toLowerCase() === mood.name.toLowerCase());
      }
    }

    // Apply search query
    if (searchQuery) {
      tracks = tracks.filter(track =>
        track.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        track.artist.toLowerCase().includes(searchQuery.toLowerCase()) ||
        track.album.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    return tracks;
  };

  const handleTrackPress = (track) => {
    const filteredTracks = getFilteredTracks();
    const trackIndex = filteredTracks.findIndex(t => t.id === track.id);
    playTrack(track, filteredTracks, trackIndex);
    navigation.navigate('Player');
  };

  const handleFavoritePress = (track) => {
    if (isFavorite(track.id)) {
      removeFromFavorites(track.id);
    } else {
      addToFavorites(track.id);
    }
  };

  const formatDuration = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const renderTrack = ({ item }) => (
    <TouchableOpacity
      style={styles.trackItem}
      onPress={() => handleTrackPress(item)}
    >
      <Image source={{ uri: item.coverImage }} style={styles.trackCover} />
      <View style={styles.trackInfo}>
        <Text style={styles.trackTitle} numberOfLines={1}>
          {item.title}
        </Text>
        <Text style={styles.trackArtist} numberOfLines={1}>
          {item.artist}
        </Text>
        <View style={styles.trackMeta}>
          <Text style={styles.trackCategory}>{item.category}</Text>
          <Text style={styles.trackDuration}>{formatDuration(item.duration)}</Text>
        </View>
      </View>
      <TouchableOpacity
        style={styles.favoriteButton}
        onPress={() => handleFavoritePress(item)}
      >
        <Ionicons
          name={isFavorite(item.id) ? 'heart' : 'heart-outline'}
          size={24}
          color={isFavorite(item.id) ? '#FF6B9D' : '#8e8e93'}
        />
      </TouchableOpacity>
    </TouchableOpacity>
  );

  const renderFilterChip = (filter) => {
    const isSelected = selectedFilter === filter.id;
    return (
      <TouchableOpacity
        key={filter.id}
        style={[
          styles.filterChip,
          isSelected && styles.filterChipSelected,
          filter.color && { backgroundColor: isSelected ? filter.color : 'transparent' }
        ]}
        onPress={() => setSelectedFilter(filter.id)}
      >
        <Text
          style={[
            styles.filterChipText,
            isSelected && styles.filterChipTextSelected
          ]}
        >
          {filter.name}
        </Text>
      </TouchableOpacity>
    );
  };

  const filters = [
    { id: 'all', name: 'All' },
    { id: 'favorites', name: 'Favorites', color: '#FF6B9D' },
    ...categories,
    ...moods,
  ];

  const filteredTracks = getFilteredTracks();

  return (
    <SafeAreaView style={styles.container}>
      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#8e8e93" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search songs, artists, albums..."
          placeholderTextColor="#8e8e93"
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        {searchQuery ? (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <Ionicons name="close-circle" size={20} color="#8e8e93" />
          </TouchableOpacity>
        ) : null}
      </View>

      {/* Filter Chips */}
      <View style={styles.filtersContainer}>
        <FlatList
          data={filters}
          renderItem={({ item }) => renderFilterChip(item)}
          keyExtractor={(item) => item.id}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.filtersList}
        />
      </View>

      {/* Results Count */}
      <View style={styles.resultsContainer}>
        <Text style={styles.resultsText}>
          {filteredTracks.length} song{filteredTracks.length !== 1 ? 's' : ''}
        </Text>
      </View>

      {/* Tracks List */}
      <FlatList
        data={filteredTracks}
        renderItem={renderTrack}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.tracksList}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="musical-notes-outline" size={64} color="#8e8e93" />
            <Text style={styles.emptyText}>No songs found</Text>
            <Text style={styles.emptySubtext}>
              Try adjusting your search or filters
            </Text>
          </View>
        }
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0f0f23',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#1a1a2e',
    margin: 20,
    marginBottom: 15,
    paddingHorizontal: 15,
    paddingVertical: 12,
    borderRadius: 25,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    color: '#ffffff',
    fontSize: 16,
  },
  filtersContainer: {
    marginBottom: 15,
  },
  filtersList: {
    paddingHorizontal: 15,
  },
  filterChip: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 10,
    borderRadius: 20,
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#8e8e93',
  },
  filterChipSelected: {
    borderColor: 'transparent',
  },
  filterChipText: {
    color: '#8e8e93',
    fontSize: 14,
    fontWeight: '500',
  },
  filterChipTextSelected: {
    color: '#ffffff',
  },
  resultsContainer: {
    paddingHorizontal: 20,
    marginBottom: 10,
  },
  resultsText: {
    color: '#8e8e93',
    fontSize: 14,
  },
  tracksList: {
    paddingHorizontal: 20,
  },
  trackItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#1a1a2e',
  },
  trackCover: {
    width: 60,
    height: 60,
    borderRadius: 8,
  },
  trackInfo: {
    flex: 1,
    marginLeft: 15,
  },
  trackTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
    marginBottom: 4,
  },
  trackArtist: {
    fontSize: 14,
    color: '#8e8e93',
    marginBottom: 4,
  },
  trackMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  trackCategory: {
    fontSize: 12,
    color: '#6B73FF',
    fontWeight: '500',
  },
  trackDuration: {
    fontSize: 12,
    color: '#8e8e93',
  },
  favoriteButton: {
    padding: 10,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#ffffff',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#8e8e93',
    textAlign: 'center',
  },
});

export default LibraryScreen;
