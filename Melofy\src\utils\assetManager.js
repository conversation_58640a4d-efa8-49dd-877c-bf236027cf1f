// Asset Manager for Melofy
// Handles loading and caching of music files and cover images

import { Asset } from 'expo-asset';
import { Audio } from 'expo-av';

class AssetManager {
  constructor() {
    this.loadedAssets = new Map();
    this.audioCache = new Map();
  }

  /**
   * Preload an asset (image or audio)
   * @param {string|number} asset - Asset path or require() result
   * @param {string} type - 'image' or 'audio'
   * @returns {Promise<string>} - Local URI of the loaded asset
   */
  async preloadAsset(asset, type = 'image') {
    try {
      const assetKey = typeof asset === 'string' ? asset : asset.toString();
      
      if (this.loadedAssets.has(assetKey)) {
        return this.loadedAssets.get(assetKey);
      }

      let loadedAsset;
      
      if (typeof asset === 'string' && asset.startsWith('http')) {
        // Remote URL - return as is
        loadedAsset = asset;
      } else {
        // Local asset - use Expo Asset
        const assetModule = typeof asset === 'string' ? { uri: asset } : asset;
        const [{ localUri }] = await Asset.loadAsync(assetModule);
        loadedAsset = localUri;
      }

      this.loadedAssets.set(assetKey, loadedAsset);
      return loadedAsset;
    } catch (error) {
      console.error(`Error preloading ${type} asset:`, error);
      return null;
    }
  }

  /**
   * Preload multiple assets
   * @param {Array} assets - Array of asset objects with {asset, type}
   * @returns {Promise<Array>} - Array of loaded asset URIs
   */
  async preloadMultipleAssets(assets) {
    try {
      const promises = assets.map(({ asset, type }) => 
        this.preloadAsset(asset, type)
      );
      return await Promise.all(promises);
    } catch (error) {
      console.error('Error preloading multiple assets:', error);
      return [];
    }
  }

  /**
   * Create and cache an audio sound object
   * @param {string} audioUri - URI of the audio file
   * @param {string} trackId - Unique identifier for the track
   * @returns {Promise<Audio.Sound>} - Expo Audio Sound object
   */
  async createAudioSound(audioUri, trackId) {
    try {
      // Check if sound is already cached
      if (this.audioCache.has(trackId)) {
        const cachedSound = this.audioCache.get(trackId);
        // Check if the sound is still valid
        const status = await cachedSound.getStatusAsync();
        if (status.isLoaded) {
          return cachedSound;
        } else {
          // Remove invalid sound from cache
          this.audioCache.delete(trackId);
        }
      }

      // Create new sound object
      const { sound } = await Audio.Sound.createAsync(
        { uri: audioUri },
        { shouldPlay: false }
      );

      // Cache the sound object
      this.audioCache.set(trackId, sound);
      return sound;
    } catch (error) {
      console.error('Error creating audio sound:', error);
      return null;
    }
  }

  /**
   * Unload a cached audio sound
   * @param {string} trackId - Track identifier
   */
  async unloadAudioSound(trackId) {
    try {
      if (this.audioCache.has(trackId)) {
        const sound = this.audioCache.get(trackId);
        await sound.unloadAsync();
        this.audioCache.delete(trackId);
      }
    } catch (error) {
      console.error('Error unloading audio sound:', error);
    }
  }

  /**
   * Clear all cached audio sounds
   */
  async clearAudioCache() {
    try {
      const promises = Array.from(this.audioCache.keys()).map(trackId =>
        this.unloadAudioSound(trackId)
      );
      await Promise.all(promises);
      this.audioCache.clear();
    } catch (error) {
      console.error('Error clearing audio cache:', error);
    }
  }

  /**
   * Get cached asset URI
   * @param {string|number} asset - Asset identifier
   * @returns {string|null} - Cached asset URI or null
   */
  getCachedAsset(asset) {
    const assetKey = typeof asset === 'string' ? asset : asset.toString();
    return this.loadedAssets.get(assetKey) || null;
  }

  /**
   * Clear all cached assets
   */
  clearAssetCache() {
    this.loadedAssets.clear();
  }

  /**
   * Get cache statistics
   * @returns {Object} - Cache statistics
   */
  getCacheStats() {
    return {
      loadedAssets: this.loadedAssets.size,
      cachedAudioSounds: this.audioCache.size,
      totalCacheSize: this.loadedAssets.size + this.audioCache.size
    };
  }

  /**
   * Preload track assets (cover image and audio)
   * @param {Object} track - Track object with coverImage and filePath
   * @returns {Promise<Object>} - Object with loaded asset URIs
   */
  async preloadTrackAssets(track) {
    try {
      const [coverUri, audioUri] = await Promise.all([
        this.preloadAsset(track.coverImage, 'image'),
        this.preloadAsset(track.filePath, 'audio')
      ]);

      return {
        id: track.id,
        coverUri,
        audioUri,
        originalTrack: track
      };
    } catch (error) {
      console.error('Error preloading track assets:', error);
      return {
        id: track.id,
        coverUri: null,
        audioUri: null,
        originalTrack: track
      };
    }
  }

  /**
   * Preload assets for multiple tracks
   * @param {Array} tracks - Array of track objects
   * @returns {Promise<Array>} - Array of loaded track assets
   */
  async preloadTracksAssets(tracks) {
    try {
      const promises = tracks.map(track => this.preloadTrackAssets(track));
      return await Promise.all(promises);
    } catch (error) {
      console.error('Error preloading tracks assets:', error);
      return [];
    }
  }
}

// Create and export a singleton instance
const assetManager = new AssetManager();

export default assetManager;

// Export utility functions
export const preloadAsset = (asset, type) => assetManager.preloadAsset(asset, type);
export const preloadTrackAssets = (track) => assetManager.preloadTrackAssets(track);
export const preloadTracksAssets = (tracks) => assetManager.preloadTracksAssets(tracks);
export const createAudioSound = (audioUri, trackId) => assetManager.createAudioSound(audioUri, trackId);
export const clearAudioCache = () => assetManager.clearAudioCache();
export const getCacheStats = () => assetManager.getCacheStats();
