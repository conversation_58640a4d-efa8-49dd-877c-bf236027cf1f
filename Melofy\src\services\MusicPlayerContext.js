import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { Audio } from 'expo-av';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Initial state for the music player
const initialState = {
  currentTrack: null,
  isPlaying: false,
  isLoading: false,
  position: 0,
  duration: 0,
  playlist: [],
  currentIndex: 0,
  repeatMode: 'none', // 'none', 'one', 'all'
  shuffleMode: false,
  favorites: [],
  sound: null,
};

// Action types
const ACTIONS = {
  SET_CURRENT_TRACK: 'SET_CURRENT_TRACK',
  SET_PLAYING: 'SET_PLAYING',
  SET_LOADING: 'SET_LOADING',
  SET_POSITION: 'SET_POSITION',
  SET_DURATION: 'SET_DURATION',
  SET_PLAYLIST: 'SET_PLAYLIST',
  SET_CURRENT_INDEX: 'SET_CURRENT_INDEX',
  SET_REPEAT_MODE: 'SET_REPEAT_MODE',
  SET_SHUFFLE_MODE: 'SET_SHUFFLE_MODE',
  SET_FAVORITES: 'SET_FAVORITES',
  ADD_FAVORITE: 'ADD_FAVORITE',
  REMOVE_FAVORITE: 'REMOVE_FAVORITE',
  SET_SOUND: 'SET_SOUND',
};

// Reducer function
function musicPlayerReducer(state, action) {
  switch (action.type) {
    case ACTIONS.SET_CURRENT_TRACK:
      return { ...state, currentTrack: action.payload };
    case ACTIONS.SET_PLAYING:
      return { ...state, isPlaying: action.payload };
    case ACTIONS.SET_LOADING:
      return { ...state, isLoading: action.payload };
    case ACTIONS.SET_POSITION:
      return { ...state, position: action.payload };
    case ACTIONS.SET_DURATION:
      return { ...state, duration: action.payload };
    case ACTIONS.SET_PLAYLIST:
      return { ...state, playlist: action.payload };
    case ACTIONS.SET_CURRENT_INDEX:
      return { ...state, currentIndex: action.payload };
    case ACTIONS.SET_REPEAT_MODE:
      return { ...state, repeatMode: action.payload };
    case ACTIONS.SET_SHUFFLE_MODE:
      return { ...state, shuffleMode: action.payload };
    case ACTIONS.SET_FAVORITES:
      return { ...state, favorites: action.payload };
    case ACTIONS.ADD_FAVORITE:
      return { 
        ...state, 
        favorites: [...state.favorites, action.payload] 
      };
    case ACTIONS.REMOVE_FAVORITE:
      return { 
        ...state, 
        favorites: state.favorites.filter(id => id !== action.payload) 
      };
    case ACTIONS.SET_SOUND:
      return { ...state, sound: action.payload };
    default:
      return state;
  }
}

// Create context
const MusicPlayerContext = createContext();

// Custom hook to use the music player context
export const useMusicPlayer = () => {
  const context = useContext(MusicPlayerContext);
  if (!context) {
    throw new Error('useMusicPlayer must be used within a MusicPlayerProvider');
  }
  return context;
};

// Provider component
export const MusicPlayerProvider = ({ children }) => {
  const [state, dispatch] = useReducer(musicPlayerReducer, initialState);

  // Load favorites from AsyncStorage on app start
  useEffect(() => {
    loadFavorites();
    setupAudio();
  }, []);

  // Setup audio configuration
  const setupAudio = async () => {
    try {
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: false,
        staysActiveInBackground: true,
        interruptionModeIOS: Audio.INTERRUPTION_MODE_IOS_DO_NOT_MIX,
        playsInSilentModeIOS: true,
        shouldDuckAndroid: true,
        interruptionModeAndroid: Audio.INTERRUPTION_MODE_ANDROID_DO_NOT_MIX,
        playThroughEarpieceAndroid: false,
      });
    } catch (error) {
      console.error('Error setting up audio:', error);
    }
  };

  // Load favorites from local storage
  const loadFavorites = async () => {
    try {
      const favorites = await AsyncStorage.getItem('favorites');
      if (favorites) {
        dispatch({ type: ACTIONS.SET_FAVORITES, payload: JSON.parse(favorites) });
      }
    } catch (error) {
      console.error('Error loading favorites:', error);
    }
  };

  // Save favorites to local storage
  const saveFavorites = async (favorites) => {
    try {
      await AsyncStorage.setItem('favorites', JSON.stringify(favorites));
    } catch (error) {
      console.error('Error saving favorites:', error);
    }
  };

  // Play a track
  const playTrack = async (track, playlist = [], index = 0) => {
    try {
      dispatch({ type: ACTIONS.SET_LOADING, payload: true });

      // Stop current sound if playing
      if (state.sound) {
        await state.sound.unloadAsync();
      }

      // Create new sound object
      const { sound } = await Audio.Sound.createAsync(
        { uri: track.filePath },
        { shouldPlay: true }
      );

      dispatch({ type: ACTIONS.SET_SOUND, payload: sound });
      dispatch({ type: ACTIONS.SET_CURRENT_TRACK, payload: track });
      dispatch({ type: ACTIONS.SET_PLAYLIST, payload: playlist });
      dispatch({ type: ACTIONS.SET_CURRENT_INDEX, payload: index });
      dispatch({ type: ACTIONS.SET_PLAYING, payload: true });
      dispatch({ type: ACTIONS.SET_LOADING, payload: false });

      // Set up playback status update
      sound.setOnPlaybackStatusUpdate(onPlaybackStatusUpdate);

    } catch (error) {
      console.error('Error playing track:', error);
      dispatch({ type: ACTIONS.SET_LOADING, payload: false });
    }
  };

  // Playback status update handler
  const onPlaybackStatusUpdate = (status) => {
    if (status.isLoaded) {
      dispatch({ type: ACTIONS.SET_POSITION, payload: status.positionMillis });
      dispatch({ type: ACTIONS.SET_DURATION, payload: status.durationMillis });
      dispatch({ type: ACTIONS.SET_PLAYING, payload: status.isPlaying });

      // Handle track end
      if (status.didJustFinish) {
        handleTrackEnd();
      }
    }
  };

  // Handle when track ends
  const handleTrackEnd = () => {
    if (state.repeatMode === 'one') {
      // Repeat current track
      playTrack(state.currentTrack, state.playlist, state.currentIndex);
    } else if (state.currentIndex < state.playlist.length - 1) {
      // Play next track
      playNext();
    } else if (state.repeatMode === 'all') {
      // Restart playlist
      playTrack(state.playlist[0], state.playlist, 0);
    } else {
      // Stop playing
      dispatch({ type: ACTIONS.SET_PLAYING, payload: false });
    }
  };

  // Play/pause toggle
  const togglePlayPause = async () => {
    if (state.sound) {
      if (state.isPlaying) {
        await state.sound.pauseAsync();
      } else {
        await state.sound.playAsync();
      }
    }
  };

  // Play next track
  const playNext = () => {
    if (state.playlist.length > 0) {
      const nextIndex = (state.currentIndex + 1) % state.playlist.length;
      playTrack(state.playlist[nextIndex], state.playlist, nextIndex);
    }
  };

  // Play previous track
  const playPrevious = () => {
    if (state.playlist.length > 0) {
      const prevIndex = state.currentIndex === 0 
        ? state.playlist.length - 1 
        : state.currentIndex - 1;
      playTrack(state.playlist[prevIndex], state.playlist, prevIndex);
    }
  };

  // Seek to position
  const seekTo = async (position) => {
    if (state.sound) {
      await state.sound.setPositionAsync(position);
    }
  };

  // Toggle repeat mode
  const toggleRepeatMode = () => {
    const modes = ['none', 'one', 'all'];
    const currentIndex = modes.indexOf(state.repeatMode);
    const nextMode = modes[(currentIndex + 1) % modes.length];
    dispatch({ type: ACTIONS.SET_REPEAT_MODE, payload: nextMode });
  };

  // Toggle shuffle mode
  const toggleShuffleMode = () => {
    dispatch({ type: ACTIONS.SET_SHUFFLE_MODE, payload: !state.shuffleMode });
  };

  // Add to favorites
  const addToFavorites = async (trackId) => {
    const newFavorites = [...state.favorites, trackId];
    dispatch({ type: ACTIONS.ADD_FAVORITE, payload: trackId });
    await saveFavorites(newFavorites);
  };

  // Remove from favorites
  const removeFromFavorites = async (trackId) => {
    const newFavorites = state.favorites.filter(id => id !== trackId);
    dispatch({ type: ACTIONS.REMOVE_FAVORITE, payload: trackId });
    await saveFavorites(newFavorites);
  };

  // Check if track is favorite
  const isFavorite = (trackId) => {
    return state.favorites.includes(trackId);
  };

  const value = {
    ...state,
    playTrack,
    togglePlayPause,
    playNext,
    playPrevious,
    seekTo,
    toggleRepeatMode,
    toggleShuffleMode,
    addToFavorites,
    removeFromFavorites,
    isFavorite,
  };

  return (
    <MusicPlayerContext.Provider value={value}>
      {children}
    </MusicPlayerContext.Provider>
  );
};
