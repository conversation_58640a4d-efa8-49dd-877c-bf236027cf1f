# Melofy Assets Management Guide

## Overview
This guide explains how to manage music files and cover images in your Melofy app.

## Directory Structure
```
assets/
├── music/           # Audio files (.mp3, .wav, .m4a)
├── covers/          # Album cover images (.jpg, .png)
├── icon.png         # App icon
├── splash-icon.png  # Splash screen icon
├── adaptive-icon.png # Android adaptive icon
└── favicon.png      # Web favicon
```

## Adding Music Files

### Step 1: Add Audio Files
1. Place your music files in the `assets/music/` directory
2. Supported formats: MP3, WAV, M4A, AAC
3. Recommended naming: `artist-title.mp3` (lowercase, hyphens instead of spaces)

### Step 2: Add Cover Images
1. Place cover images in the `assets/covers/` directory
2. Recommended size: 300x300px or higher (square aspect ratio)
3. Supported formats: JPG, PNG
4. Naming should match your music data structure

### Step 3: Update Music Data
Edit `src/data/musicData.js` to reference your local assets:

```javascript
{
  id: '1',
  title: 'Your Song Title',
  artist: 'Artist Name',
  album: 'Album Name',
  duration: 245, // in seconds
  filePath: require('../../assets/music/your-song.mp3'),
  coverImage: require('../../assets/covers/your-cover.jpg'),
  category: 'Pop', // or 'LoFi', 'Classical'
  mood: 'Happy' // or 'Relax', 'Focus'
}
```

## Asset Optimization Tips

### Audio Files
- Use MP3 format for best compatibility and size
- Bitrate: 128-320 kbps (192 kbps recommended for good quality/size balance)
- Keep file sizes reasonable (3-8MB per song)

### Cover Images
- Use JPG for photographs, PNG for graphics with transparency
- Optimize images to reduce file size while maintaining quality
- Consider using tools like TinyPNG or ImageOptim

## Bundle Size Considerations

### For Development
- Start with a few sample tracks to test functionality
- Use placeholder URLs during development (as currently implemented)

### For Production
- Consider hosting large audio files on a CDN or server
- Bundle only essential assets with the app
- Implement lazy loading for non-critical assets

## Current Implementation

The app currently uses placeholder URLs for testing:
- Audio: `https://www.soundjay.com/misc/sounds/bell-ringing-05.wav`
- Images: `https://picsum.photos/300/300?random=X`

To switch to local assets:
1. Add your files to the appropriate directories
2. Update the `musicData.js` file to use `require()` statements
3. Test the app to ensure assets load correctly

## Adding New Categories/Moods

To add new categories or moods:
1. Update the `categories` or `moods` arrays in `musicData.js`
2. Add appropriate icons from Ionicons
3. Choose distinctive colors for each category/mood
4. Add tracks with the new category/mood values

## Example Local Asset Configuration

```javascript
// Example of local asset usage
{
  id: 'local1',
  title: 'Chill Beats',
  artist: 'Local Artist',
  album: 'Demo Album',
  duration: 180,
  filePath: require('../../assets/music/chill-beats.mp3'),
  coverImage: require('../../assets/covers/chill-beats.jpg'),
  category: 'LoFi',
  mood: 'Relax'
}
```

## Testing Assets
1. Run `npm start` to start the development server
2. Test on web first: `npx expo start --web`
3. Test on Android: `npx expo start --android` (requires Android Studio/device)
4. Verify all assets load correctly and audio plays properly

## Troubleshooting

### Assets Not Loading
- Check file paths are correct
- Ensure files exist in the specified directories
- Verify file formats are supported
- Check for typos in filenames

### Large Bundle Size
- Consider using remote URLs for audio files
- Optimize image sizes
- Remove unused assets
- Use asset bundling strategies for production

### Audio Playback Issues
- Test with different audio formats
- Check file corruption
- Verify Expo AV compatibility
- Test on different devices/platforms
