# Melofy Android Build Guide

## Overview
This guide covers Android-specific configuration, optimization, and build process for the Melofy music streaming app.

## Prerequisites
1. **Android Studio** - Download and install from https://developer.android.com/studio
2. **Java Development Kit (JDK)** - JDK 11 or higher
3. **Android SDK** - Installed via Android Studio
4. **Expo CLI** - Already installed with the project

## Android Configuration

### Current Configuration (app.json)
```json
{
  "android": {
    "adaptiveIcon": {
      "foregroundImage": "./assets/adaptive-icon.png",
      "backgroundColor": "#1a1a2e"
    },
    "edgeToEdgeEnabled": true,
    "package": "com.melofy.app",
    "versionCode": 1,
    "permissions": [
      "WAKE_LOCK",
      "FOREGROUND_SERVICE"
    ],
    "playStoreUrl": "https://play.google.com/store/apps/details?id=com.melofy.app"
  }
}
```

### Key Android Features
- **Background Audio Playback**: Configured with <PERSON>KE_LOCK and FOREGROUND_SERVICE permissions
- **Edge-to-Edge Display**: Modern Android UI with edge-to-edge content
- **Adaptive Icon**: Supports Android's adaptive icon system
- **Dark Theme**: Optimized for Android's dark theme

## Building for Android

### Development Build
```bash
# Start development server
npx expo start

# Open on Android device/emulator
npx expo start --android
```

### Production Build (EAS Build)
```bash
# Install EAS CLI
npm install -g @expo/eas-cli

# Login to Expo
npx eas login

# Configure build
npx eas build:configure

# Build for Android
npx eas build --platform android
```

### Local Build (if needed)
```bash
# Generate native code
npx expo prebuild --platform android

# Build with Android Studio or Gradle
cd android
./gradlew assembleRelease
```

## Android Optimization

### Performance Optimizations
1. **Audio Optimization**
   - Uses Expo AV with Android-optimized settings
   - Background playback with proper service configuration
   - Audio focus management for interruptions

2. **Memory Management**
   - Asset caching with proper cleanup
   - Image optimization for different screen densities
   - Lazy loading of non-critical components

3. **Battery Optimization**
   - Efficient background processing
   - Proper wake lock management
   - Optimized audio codec usage

### UI/UX Optimizations
1. **Material Design Elements**
   - Uses Android-appropriate colors and spacing
   - Proper touch targets (48dp minimum)
   - Android navigation patterns

2. **Screen Compatibility**
   - Responsive design for different screen sizes
   - Support for various Android screen densities
   - Edge-to-edge display support

## Testing on Android

### Using Android Emulator
1. Open Android Studio
2. Go to Tools > AVD Manager
3. Create a new Virtual Device (recommended: Pixel 4 with API 30+)
4. Start the emulator
5. Run `npx expo start --android`

### Using Physical Device
1. Enable Developer Options on your Android device
2. Enable USB Debugging
3. Connect device via USB
4. Run `npx expo start --android`
5. Or scan QR code with Expo Go app

### Testing Checklist
- [ ] App launches successfully
- [ ] Navigation works smoothly
- [ ] Audio playback functions correctly
- [ ] Background playback works
- [ ] Favorites are saved locally
- [ ] App handles interruptions (calls, notifications)
- [ ] UI looks good on different screen sizes
- [ ] Performance is smooth (60fps)

## Android-Specific Features

### Background Audio Service
The app is configured to support background audio playback:
- Uses `WAKE_LOCK` permission to keep CPU active during playback
- Uses `FOREGROUND_SERVICE` for background audio processing
- Properly handles audio focus changes

### Notification Controls (Future Enhancement)
For production, consider adding:
```javascript
// Media notification controls
import { Audio } from 'expo-av';

// Configure audio session
await Audio.setAudioModeAsync({
  staysActiveInBackground: true,
  interruptionModeAndroid: Audio.INTERRUPTION_MODE_ANDROID_DO_NOT_MIX,
  shouldDuckAndroid: true,
  playThroughEarpieceAndroid: false,
});
```

## Build Configuration Files

### EAS Build Configuration (eas.json)
Create this file for production builds:
```json
{
  "cli": {
    "version": ">= 3.0.0"
  },
  "build": {
    "development": {
      "developmentClient": true,
      "distribution": "internal",
      "android": {
        "gradleCommand": ":app:assembleDebug"
      }
    },
    "preview": {
      "distribution": "internal",
      "android": {
        "buildType": "apk"
      }
    },
    "production": {
      "android": {
        "buildType": "aab"
      }
    }
  },
  "submit": {
    "production": {}
  }
}
```

## Troubleshooting

### Common Issues
1. **Audio not playing**
   - Check device volume
   - Verify audio permissions
   - Test with different audio formats

2. **App crashes on startup**
   - Check for missing dependencies
   - Verify Android SDK version compatibility
   - Check device logs with `adb logcat`

3. **Build failures**
   - Clear Metro cache: `npx expo start --clear`
   - Clean node modules: `rm -rf node_modules && npm install`
   - Check Java/Android SDK versions

### Debug Commands
```bash
# View device logs
adb logcat

# Clear Metro cache
npx expo start --clear

# Reset Expo cache
npx expo start --reset-cache

# Check connected devices
adb devices
```

## Performance Monitoring

### Key Metrics to Monitor
- App startup time
- Audio playback latency
- Memory usage during playback
- Battery consumption
- UI responsiveness (frame rate)

### Tools for Monitoring
- Android Studio Profiler
- Expo DevTools
- React Native Performance Monitor
- Android Device Monitor

## Publishing to Google Play Store

### Preparation Steps
1. Create Google Play Console account
2. Generate signed APK/AAB
3. Prepare store listing (screenshots, description)
4. Set up app pricing and distribution
5. Complete content rating questionnaire

### Required Assets
- App icon (512x512px)
- Feature graphic (1024x500px)
- Screenshots (various sizes)
- Privacy policy URL
- App description and metadata

## Security Considerations

### Android Security Features
- App signing with proper certificates
- ProGuard/R8 code obfuscation for release builds
- Secure storage for user preferences
- Network security configuration

### Best Practices
- Use HTTPS for all network requests
- Validate all user inputs
- Implement proper error handling
- Use secure storage for sensitive data

## Next Steps

1. Test the current build on Android devices
2. Optimize performance based on testing results
3. Add Android-specific features (notifications, widgets)
4. Prepare for Play Store submission
5. Set up continuous integration/deployment
