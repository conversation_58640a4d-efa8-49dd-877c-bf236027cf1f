import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Dimensions,
  Slider,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';

import { useMusicPlayer } from '../services/MusicPlayerContext';

const { width, height } = Dimensions.get('window');

const PlayerScreen = ({ navigation }) => {
  const {
    currentTrack,
    isPlaying,
    position,
    duration,
    repeatMode,
    shuffleMode,
    togglePlayPause,
    playNext,
    playPrevious,
    seekTo,
    toggleRepeatMode,
    toggleShuffleMode,
    isFavorite,
    addToFavorites,
    removeFromFavorites,
  } = useMusicPlayer();

  const [isSliding, setIsSliding] = useState(false);
  const [sliderValue, setSliderValue] = useState(0);

  useEffect(() => {
    if (!isSliding && duration > 0) {
      setSliderValue(position / duration);
    }
  }, [position, duration, isSliding]);

  const formatTime = (milliseconds) => {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleSliderChange = (value) => {
    setSliderValue(value);
  };

  const handleSliderComplete = (value) => {
    setIsSliding(false);
    if (duration > 0) {
      seekTo(value * duration);
    }
  };

  const handleFavoritePress = () => {
    if (currentTrack) {
      if (isFavorite(currentTrack.id)) {
        removeFromFavorites(currentTrack.id);
      } else {
        addToFavorites(currentTrack.id);
      }
    }
  };

  const getRepeatIcon = () => {
    switch (repeatMode) {
      case 'one':
        return 'repeat-outline';
      case 'all':
        return 'repeat';
      default:
        return 'repeat-outline';
    }
  };

  if (!currentTrack) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.emptyContainer}>
          <Ionicons name="musical-notes-outline" size={64} color="#8e8e93" />
          <Text style={styles.emptyText}>No track selected</Text>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.backButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <LinearGradient
      colors={['#1a1a2e', '#16213e', '#0f0f23']}
      style={styles.container}
    >
      <SafeAreaView style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="chevron-down" size={28} color="#ffffff" />
          </TouchableOpacity>
          <View style={styles.headerInfo}>
            <Text style={styles.headerTitle}>Now Playing</Text>
            <Text style={styles.headerSubtitle}>From {currentTrack.category}</Text>
          </View>
          <TouchableOpacity style={styles.headerButton}>
            <Ionicons name="ellipsis-horizontal" size={28} color="#ffffff" />
          </TouchableOpacity>
        </View>

        {/* Album Art */}
        <View style={styles.albumArtContainer}>
          <Image
            source={{ uri: currentTrack.coverImage }}
            style={styles.albumArt}
          />
        </View>

        {/* Track Info */}
        <View style={styles.trackInfo}>
          <Text style={styles.trackTitle} numberOfLines={1}>
            {currentTrack.title}
          </Text>
          <Text style={styles.trackArtist} numberOfLines={1}>
            {currentTrack.artist}
          </Text>
          <Text style={styles.trackAlbum} numberOfLines={1}>
            {currentTrack.album}
          </Text>
        </View>

        {/* Progress Bar */}
        <View style={styles.progressContainer}>
          <Slider
            style={styles.slider}
            value={sliderValue}
            onValueChange={handleSliderChange}
            onSlidingStart={() => setIsSliding(true)}
            onSlidingComplete={handleSliderComplete}
            minimumValue={0}
            maximumValue={1}
            minimumTrackTintColor="#6B73FF"
            maximumTrackTintColor="#333333"
            thumbStyle={styles.sliderThumb}
          />
          <View style={styles.timeContainer}>
            <Text style={styles.timeText}>{formatTime(position)}</Text>
            <Text style={styles.timeText}>{formatTime(duration)}</Text>
          </View>
        </View>

        {/* Controls */}
        <View style={styles.controlsContainer}>
          {/* Secondary Controls */}
          <View style={styles.secondaryControls}>
            <TouchableOpacity
              style={[
                styles.controlButton,
                shuffleMode && styles.controlButtonActive
              ]}
              onPress={toggleShuffleMode}
            >
              <Ionicons
                name="shuffle"
                size={24}
                color={shuffleMode ? '#6B73FF' : '#8e8e93'}
              />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.controlButton}
              onPress={handleFavoritePress}
            >
              <Ionicons
                name={isFavorite(currentTrack.id) ? 'heart' : 'heart-outline'}
                size={24}
                color={isFavorite(currentTrack.id) ? '#FF6B9D' : '#8e8e93'}
              />
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.controlButton,
                repeatMode !== 'none' && styles.controlButtonActive
              ]}
              onPress={toggleRepeatMode}
            >
              <Ionicons
                name={getRepeatIcon()}
                size={24}
                color={repeatMode !== 'none' ? '#6B73FF' : '#8e8e93'}
              />
            </TouchableOpacity>
          </View>

          {/* Main Controls */}
          <View style={styles.mainControls}>
            <TouchableOpacity
              style={styles.controlButton}
              onPress={playPrevious}
            >
              <Ionicons name="play-skip-back" size={32} color="#ffffff" />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.playButton}
              onPress={togglePlayPause}
            >
              <Ionicons
                name={isPlaying ? 'pause' : 'play'}
                size={36}
                color="#ffffff"
              />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.controlButton}
              onPress={playNext}
            >
              <Ionicons name="play-skip-forward" size={32} color="#ffffff" />
            </TouchableOpacity>
          </View>
        </View>
      </SafeAreaView>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  headerButton: {
    width: 44,
    height: 44,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerInfo: {
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  headerSubtitle: {
    fontSize: 12,
    color: '#8e8e93',
    marginTop: 2,
  },
  albumArtContainer: {
    alignItems: 'center',
    marginVertical: 40,
  },
  albumArt: {
    width: width * 0.8,
    height: width * 0.8,
    borderRadius: 20,
  },
  trackInfo: {
    alignItems: 'center',
    paddingHorizontal: 40,
    marginBottom: 40,
  },
  trackTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 8,
    textAlign: 'center',
  },
  trackArtist: {
    fontSize: 18,
    color: '#8e8e93',
    marginBottom: 4,
    textAlign: 'center',
  },
  trackAlbum: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
  },
  progressContainer: {
    paddingHorizontal: 30,
    marginBottom: 40,
  },
  slider: {
    width: '100%',
    height: 40,
  },
  sliderThumb: {
    backgroundColor: '#6B73FF',
    width: 20,
    height: 20,
  },
  timeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  timeText: {
    fontSize: 12,
    color: '#8e8e93',
  },
  controlsContainer: {
    paddingHorizontal: 40,
  },
  secondaryControls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 30,
  },
  mainControls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  controlButton: {
    width: 50,
    height: 50,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 15,
  },
  controlButtonActive: {
    backgroundColor: 'rgba(107, 115, 255, 0.2)',
    borderRadius: 25,
  },
  playButton: {
    width: 80,
    height: 80,
    backgroundColor: '#6B73FF',
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 20,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#ffffff',
    marginTop: 16,
    marginBottom: 30,
  },
  backButton: {
    backgroundColor: '#6B73FF',
    paddingHorizontal: 30,
    paddingVertical: 12,
    borderRadius: 25,
  },
  backButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default PlayerScreen;
